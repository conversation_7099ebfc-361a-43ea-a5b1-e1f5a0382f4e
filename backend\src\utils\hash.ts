import bcrypt from 'bcryptjs';

const SALT_ROUNDS = 12;

export const hashPin = async (pin: string): Promise<string> => {
  return await bcrypt.hash(pin, SALT_ROUNDS);
};

export const comparePin = async (pin: string, hashedPin: string): Promise<boolean> => {
  return await bcrypt.compare(pin, hashedPin);
};

export const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, SALT_ROUNDS);
};

export const comparePassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return await bcrypt.compare(password, hashedPassword);
};

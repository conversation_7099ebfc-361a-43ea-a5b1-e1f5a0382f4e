import { z } from 'zod';

// PIN Login validation schema
export const pinLoginSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  pin: z.string()
    .length(6, 'PIN must be exactly 6 digits')
    .regex(/^\d{6}$/, 'PIN must contain only digits'),
});

// Validation middleware helper
export const validateRequest = (schema: z.ZodSchema) => {
  return (req: any, res: any, next: any) => {
    try {
      schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: error.errors[0].message,
        });
      }
      return res.status(400).json({
        success: false,
        error: 'Invalid request data',
      });
    }
  };
};

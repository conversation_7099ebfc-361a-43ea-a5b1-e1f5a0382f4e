// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// User Types
export interface UserShiftInfo {
  id: string;
  firstName: string;
  lastName: string;
  username: string;
  role: string;
  shiftDetails: string;
}

// Auth Types
export interface PinLoginRequest {
  userId: string;
  pin: string;
}

export interface PinLoginResponse {
  success: boolean;
  token?: string;
  user?: {
    id: string;
    username: string;
    role: string;
    firstName: string;
    lastName: string;
  };
  shiftStarted?: boolean;
  error?: string;
}

// JWT Payload
export interface JwtPayload {
  userId: string;
  username: string;
  role: string;
  branchId?: string;
  iat?: number;
  exp?: number;
}

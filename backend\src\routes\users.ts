import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHand<PERSON> } from '../middleware/error.js';
import { ApiResponse, UserShiftInfo } from '../types/api.js';

const router = Router();
const prisma = new PrismaClient();

// GET /api/users/available-shifts
// Seçili şubeye ait aktif/vardiya başlangıcı bekleyen kullanıcı listesi
router.get('/available-shifts', asyncHandler(async (req: Request, res: Response) => {
  try {
    // Aktif kullanıcıları getir (PIN'i olan ve aktif olanlar)
    const users = await prisma.user.findMany({
      where: {
        active: true,
        pin: {
          not: null, // PIN'i olan kullanıcılar
        },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true,
        branchId: true,
        branch: {
          select: {
            name: true,
            openingTime: true,
            closingTime: true,
          },
        },
      },
      orderBy: {
        firstName: 'asc',
      },
    });

    // Kullanıcı listesini API formatına dönüştür
    const userShifts: UserShiftInfo[] = users.map(user => {
      // Vardiya detaylarını oluştur
      let shiftDetails = 'Vardiya bilgisi yok';
      
      if (user.branch?.openingTime && user.branch?.closingTime) {
        shiftDetails = `${user.branch.openingTime} - ${user.branch.closingTime}`;
      }

      return {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        username: user.username,
        role: user.role,
        shiftDetails,
      };
    });

    const response: ApiResponse<UserShiftInfo[]> = {
      success: true,
      data: userShifts,
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching available shifts:', error);
    
    const response: ApiResponse = {
      success: false,
      error: 'Kullanıcı listesi alınırken hata oluştu',
    };

    res.status(500).json(response);
  }
}));

export default router;

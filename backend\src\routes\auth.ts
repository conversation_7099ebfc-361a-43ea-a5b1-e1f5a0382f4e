import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHand<PERSON> } from '../middleware/error.js';
import { validateRequest } from '../utils/validation.js';
import { pinLoginSchema } from '../utils/validation.js';
import { comparePin } from '../utils/hash.js';
import { generateToken } from '../utils/jwt.js';
import { ApiResponse, PinLoginRequest, PinLoginResponse } from '../types/api.js';

const router = Router();
const prisma = new PrismaClient();

// POST /api/auth/pin-login
// PIN ile kullanıcı girişi ve vardiya başlatma
router.post('/pin-login', 
  validateRequest(pinLoginSchema),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { userId, pin }: PinLoginRequest = req.body;

      // Kullanıcıyı bul
      const user = await prisma.user.findFirst({
        where: {
          id: userId,
          active: true,
          pin: {
            not: null, // PIN'i olan kullanıcılar
          },
        },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!user) {
        const response: PinLoginResponse = {
          success: false,
          error: 'Kullanıcı bulunamadı veya aktif değil',
        };
        return res.status(404).json(response);
      }

      if (!user.pin) {
        const response: PinLoginResponse = {
          success: false,
          error: 'Kullanıcının PIN\'i tanımlanmamış',
        };
        return res.status(400).json(response);
      }

      // PIN'i doğrula
      const isPinValid = await comparePin(pin, user.pin);
      
      if (!isPinValid) {
        const response: PinLoginResponse = {
          success: false,
          error: 'Geçersiz PIN',
        };
        return res.status(401).json(response);
      }

      // JWT token oluştur
      const token = generateToken({
        userId: user.id,
        username: user.username,
        role: user.role,
        branchId: user.branchId || undefined,
      });

      // Session kaydı oluştur
      const session = await prisma.session.create({
        data: {
          userId: user.id,
          branchId: user.branchId || user.branch?.id || '',
          token,
          deviceInfo: req.headers['user-agent'] || 'Unknown Device',
        },
      });

      // Kullanıcının lastLoginAt alanını güncelle
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      const response: PinLoginResponse = {
        success: true,
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName,
        },
        shiftStarted: true,
      };

      res.json(response);
    } catch (error) {
      console.error('Error during PIN login:', error);
      
      const response: PinLoginResponse = {
        success: false,
        error: 'Giriş işlemi sırasında hata oluştu',
      };

      res.status(500).json(response);
    }
  })
);

export default router;
